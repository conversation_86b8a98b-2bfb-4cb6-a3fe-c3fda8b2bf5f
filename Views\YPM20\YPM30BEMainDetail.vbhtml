@Code
    If Request.IsAjaxRequest Then
        Layout = Nothing
    End If
    Dim ttSemiYear As String = ""
    Dim ttSemistry As String = "1"
    Dim id As String = "_" + Guid.NewGuid.ToString
End Code


<div id="@id" class="container-page100-white" style="align-items: normal;">

    <div class="View1 wrap-login100 p-b-30">

        <div id="divfixed1">
            @*功能名稱*@
            <span class="page100-form-label p-b-5 bo1-ColorM" style="color:black;">
                @ViewData("FuncTitle")
            </span>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">

                    <input id="btAdd" type="button" class="btn btn-default" value="新增" />
                    <input id="btSave" type="button" class="btn btn-default" value="暫存" />
                    <input id="btExit" type="button" class="btn btn-default" value="返回" />
                    <input id="btCompleteApply" type="button" class="btn btn-success" value="完成申請" />
                    
                </div>
            </div>
            @*查詢條件*@
            <div class="b2k-control">
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <label for="PropID">提案案號：</label>
                        <input id="PropID" type="text" placeholder="請輸入提案案號" style="width:auto;">
                        <span>此欄位由智權中心填寫</span>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;  
                        @*<label for="PatType">專利類別：</label>
                        <div class="dropdown" style="display: inline-block;width: auto;">
                            <button class="btn btn-default dropdown-toggle" type="button" id="PatTypeDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="width: auto; min-width: auto;">
                                <span id="PatTypeDisplay">請選擇專利類別</span> <span class="caret"></span>
                            </button>
                             <input type="text" id="PatTypeSelected" placeholder="所選專利類別將顯示於此" readonly style="width: auto; margin-left: 5px; background-color: #f9f9f9;">
                            <ul class="dropdown-menu" aria-labelledby="PatTypeDropdown" style="padding: 0px; max-width: auto;">
                                <li><label style="font-weight: normal; margin-bottom: 5px;"><input type="checkbox" name="PatType" value="PN01" onchange="updatePatentTypeDisplay()"> 發明</label></li>
                                <li><label style="font-weight: normal; margin-bottom: 5px;"><input type="checkbox" name="PatType" value="PN02" onchange="updatePatentTypeDisplay()"> 新型</label></li>
                                <li><label style="font-weight: normal; margin-bottom: 5px;"><input type="checkbox" name="PatType" value="PN03" onchange="updatePatentTypeDisplay()"> 設計</label></li>
                                <li><label style="font-weight: normal; margin-bottom: 0;"><input type="checkbox" name="PatType" value="PN04" onchange="updatePatentTypeDisplay()"> 臨時案(需於專利申請日起1年內轉正式案)</label></li>
                            </ul>
                        </div>
                       
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <label for="ApplyCountry">申請國別：</label>
                        <div class="dropdown" style="display: inline-block;width: auto;">
                            <button class="btn btn-default dropdown-toggle" type="button" id="ApplyCountryDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="width: auto; min-width: auto;">
                                <span id="ApplyCountryDisplay">請選擇申請國別</span> <span class="caret"></span>
                            </button>
                            <input type="text" id="ApplyCountrySelected" placeholder="所選申請國別將顯示於此" readonly style="width: auto; margin-left: 5px; background-color: #f9f9f9;">  
                            <ul class="dropdown-menu" aria-labelledby="ApplyCountryDropdown" style="padding: 0px; max-width: auto;">
                                <li><label style="font-weight: normal; margin-bottom: 5px;"><input type="checkbox" name="ApplyCountry" value="CN" onchange="updateApplyCountryDisplay()"> 中國</label></li>
                                <li><label style="font-weight: normal; margin-bottom: 5px;"><input type="checkbox" name="ApplyCountry" value="TW" onchange="updateApplyCountryDisplay()"> 台灣</label></li>
                                <li><label style="font-weight: normal; margin-bottom: 5px;"><input type="checkbox" name="ApplyCountry" value="US" onchange="updateApplyCountryDisplay()"> 美國</label></li>
                                <li><label style="font-weight: normal; margin-bottom: 0;"><input type="checkbox" name="ApplyCountry" value="JP" onchange="updateApplyCountryDisplay()"> 日本</label></li>
                            </ul>
                        </div>
                        
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*@  
                        <label for="FundUnit">補助單位：</label>
                        <select id="FundUnit" name="FundUnit" style="width:auto;">
                            <option value="">請選擇補助單位</option>
                            <option value="本校經費研發">本校經費研發</option>
                            <option value="國科會">國科會</option>
                            <option value="衛服部">衛服部</option>
                            <option value="經濟部">經濟部</option>
                        </select>
                    </div>
                    
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">                          
                        <label for="PropDep">申請部門：</label>
                        <input id="PropDep" type="text" placeholder="必填申請部門" style="width:auto;">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;  
                        <label for="Proposer">提案人：</label>
                        <input id="Proposer" type="text" placeholder="必填提案人" style="width:auto;">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;  
                        <label for="NumIPMeet">聯絡人：</label>
                        <input id="NumIPMeet" type="text" placeholder="必填聯絡人" style="width:auto;">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;  
                        <label for="Phone">連絡電話/校內分機：</label>
                        <input id="Phone" type="text" placeholder="必填連絡電話/校內分機" style="width:auto;">
                    </div>



                   
                </div>
            </div>


            @*資料顯示*@
            <div class="row col-lg-12 col-md-12 col-sm-12 col-xs-12"></div>

            @*申請表*@
            <div class="row col-lg-12 col-md-12 col-sm-12 col-xs-12"></div>



        </div>

        <style>
            /* ========== 頁籤容器樣式 ========== */
            #tab_Apply {
                margin-top: 20px; /* 頁籤區域與上方元素的間距 */
            }

                /* ========== 頁籤導航列表樣式 ========== */
                #tab_Apply ul {
                    list-style: none; /* 移除清單項目前的圓點 */
                    padding: 0; /* 移除預設的內邊距 */
                    margin: 0; /* 移除預設的外邊距 */
                    margin-bottom: 0; /* 確保與內容面板無間隙 */
                    border-bottom: 1px solid #ddd; /* 在導航列表底部添加分隔線（與按鈕同色）*/
                }

                /* ========== 單個頁籤項目樣式 ========== */
                #tab_Apply li {
                    display: inline-block; /* 讓頁籤項目水平排列 */
                    margin-right: 2px; /* 頁籤之間的間距 */
                }

                    /* ========== 頁籤連結樣式（未選中狀態）========== */
                    #tab_Apply li a {
                        display: block; /* 讓連結占滿整個區域，便於點擊 */
                        padding: 10px 15px; /* 內邊距：上下10px，左右15px */
                        background-color: #f5f5f5; /* 未選中頁籤的背景色（淺灰色）*/
                        border: 1px solid #ddd; /* 邊框：1px 實線 淺灰色（與按鈕同色）*/
                        border-bottom: none; /* 移除底部邊框（與內容區連接）*/
                        color: #333; /* 文字顏色：深灰色 */
                        text-decoration: none; /* 移除連結的底線 */
                        border-radius: 5px 5px 0 0; /* 圓角：左上5px, 右上5px, 右下0, 左下0（形成頁籤效果）*/
                    }

                    /* ========== 已選中頁籤的樣式 ========== */
                    #tab_Apply li.ui-tabs-active a {
                        background-color: rgb(216, 213, 213); /* 選中頁籤的背景色（淺灰色）*/
                        border: 1px solid #ddd; /* 邊框：1px 實線 淺灰色（與按鈕同色）*/
                        border-bottom: 1px solid rgb(216, 213, 213); /* 底部邊框與選中頁籤背景色相同，造成連接效果 */
                        margin-bottom: -1px; /* 向下偏移1px，覆蓋容器的底部邊框 */
                    }

                /* ========== 頁籤內容面板樣式 ========== */
                #tab_Apply .ui-tabs-panel {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    border: 1px solid #ddd; /* 內容面板的邊框（與按鈕同色）*/
                    border-top: 1px solid #ddd; /* 確保上邊框與導航列表的底邊框對齊 */
                    padding: 1px; /* 內容面板的內邊距 */
                    background-color: #fff; /* 內容面板的背景色（白色）*/
                    border-radius: 0 5px 5px 5px; /* 圓角：左上0（與選中頁籤連接），其他角5px */
                    margin-top: 0; /* 確保與導航列表無間隙 */
                }

            /* ========== 頁籤切換效果說明 ==========
             * 1. 未選中頁籤：淺灰背景 + 有底部間隙
             * 2. 選中頁籤：深灰背景 + 無底部間隙（與內容區域視覺連接）
             * 3. 內容面板：白色背景 + 邊框與導航列表完美對齊
             * 4. jQuery UI 會自動添加/移除 'ui-tabs-active' 類別
             * 5. 線條對齊技巧：
             *    - 導航列表和內容面板的 margin-bottom/margin-top 都設為 0
             *    - 選中頁籤的 margin-bottom: -1px 覆蓋導航列表的底邊框
             *    - 選中頁籤的 border-bottom 顏色與背景色相同，創造無縫連接效果
             */
        </style>


        <div id="tab_Apply">

            <ul id="ulApply">
                <li><a href="#dv_prop_app">專利申請表</a></li>
                <li><a href="#dv_prop_firm">推薦事務所</a></li>
                <li><a href="#dv_prop_tech_review">專利技術審查</a></li>
                <li><a href="#dv_prop_pre_search">前置檢索</a></li>
                <li><a href="#dv_prop_ip_review">智審會審核結果</a></li>
                @*<li><a href="#dv_prop_process">提案歷程</a></li>*@

            </ul>


            @*第一個頁籤：專利申請表*@
            <div id="dv_prop_app" class="b2k-control" style="text-align:center; border-radius:10px 10px; padding:10px; background-color:#dddcdc; overflow-y:auto; min-height:400px; max-height:calc(100vh - 200px);">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="width:100%; border-radius:10px 10px; padding:0; background-color:#dddcdc; margin:0; overflow-y:auto; min-height:400px; max-height:calc(100vh - 300px);">
                                
                    <div class="panel panel-default" >
                        <div class="panel-heading text-left" >
                                    欲申請專利名稱及本案件所屬計畫及經費資助單位
                        </div>
                        <div class="panel-body" style="text-align:left;">
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                <label for="PatName">專利申請名稱：</label>
                                <input id="PatName" type="text" placeholder="必填專利申請名稱" style="width:auto;">
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;  
                                <label for="PatNameEn">Title of invention：</label>
                                <input id="PatNameEn" type="text" placeholder="Title of invention" style="width:auto;">
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;  
                                <label for="AppDate">申請日期：</label>
                                <input id="AppDate" type="text" placeholder="請輸入申請日期" style="width:auto;" class="txDatepicker">  
                                                                                                 
                            
                            
                            
                            </div>
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                <label for="FundUnit">計畫資助機構：</label>
                                <input id="FundUnit" type="text" placeholder="請輸入計畫資助機構" style="width:auto;">
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;                                 
                                <label for="Planner">計畫主持人：</label>
                                <input id="Planner" type="text" placeholder="請輸入計畫主持人" style="width:auto;">
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;                                                                                                                                                         
                                <label for="StartDate">起始日期：</label>
                                <input id="StartDate" type="text" placeholder="請輸入起始日期" style="width:auto;" class="txDatepicker">
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;  
                                <label for="EndDate">結案日期：</label>
                                <input id="EndDate" type="text" placeholder="請輸入結案日期" style="width:auto;" class="txDatepicker">
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;                                  
                                
                                
                               
                            </div>
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                               
                               
                                    <label for="Field" style="display: inline-block; width: 50px; vertical-align: middle;">領域：</label>
                                    <label class="checkbox-inline">
                                        <input type="checkbox" name="Field" value="生技" style="vertical-align: middle;"> 生技
                                    </label>
                                    <label class="checkbox-inline">
                                        <input type="checkbox" name="Field" value="醫藥" style="vertical-align: middle;"> 醫藥
                                    </label>
                                    <label class="checkbox-inline">
                                        <input type="checkbox" name="Field" value="醫材" style="vertical-align: middle;"> 醫材
                                    </label>
                                    <label class="checkbox-inline">
                                        <input type="checkbox" name="Field" value="電資" style="vertical-align: middle;"> 電資
                                    </label>
                                    <label class="checkbox-inline">
                                        <input type="checkbox" name="Field" value="其他" id="chkFieldOther" style="vertical-align: middle;"> 其他
                                    </label>
                                    <input id="FieldOther" name="FieldOther" type="text" placeholder="請輸入其他領域" style="width:auto; display:none; margin-left: 10px;">
                             

                                <script type="text/javascript">
                                    $(function () {
                                        $('#chkFieldOther').change(function () {
                                            if ($(this).is(':checked')) {
                                                $('#FieldOther').show();
                                            } else {
                                                $('#FieldOther').hide().val('');
                                            }
                                        });
                                    });
                                </script>
                                


                            </div>
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                <label for="PatNote">專利申請備註：</label>
                                <input id="PatNote" type="text" placeholder="請輸入專利申請備註" style="width:80%;">
                            </div>
                        
                        
                        </div>
                        
                        
                    </div>
                               
                    <div class="panel panel-default">
                        <div class="panel-heading text-left" >
                                    發明人代表及發明人資料(若有其他研發機構參與專利申請，請事先與研發處智慧財產權中心聯絡)
                        </div>
                        <div class="panel-body" style="border-bottom:1px solid black;">
                            <div style="text-align:left;">
                            <label for="InventorRepresentative">發明人代表：</label>
                            <input id="InventorRepresentative" type="text" placeholder="請輸入發明人代表" style="width:auto;">
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;  
                            <label for="Phone">手機/校內分機：</label>
                            <input id="Phone" type="text" placeholder="請輸入手機/校內分機" style="width:auto;">
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;  
                            <label for="Department">所屬單位：</label>
                            <input id="Department" type="text" placeholder="請輸入所屬單位" style="width:auto;">
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <label for="JobTitle">職稱：</label>
                            <input id="JobTitle" type="text" placeholder="請輸入職稱" style="width:auto;">                            
                            </div>
                            
                         
                            <div style="text-align:left; margin-bottom:5px;">
                                  <label style="display: inline-block; width: auto; vertical-align: middle;">是否有非受雇於國防醫學大學或三軍總醫院之人員：</label>
                                  <label class="radio-inline">
                                      <input type="radio" name="IsNonEmployee" id="IsNonEmployeeYes" value="是" style="vertical-align: middle;"> 是
                                  </label>
                                  <span id="NonEmployeeDetails" style="display:none;">
                                      ，單位名稱：<input type="text" id="NonEmployeeUnit" name="NonEmployeeUnit" style="width:auto; margin-right:10px;">
                                      與合作方協議之共有比例：<input type="text" id="CooperationRatio1" name="CooperationRatio1" style="width:60px;"> : <input type="text" id="CooperationRatio2" name="CooperationRatio2" style="width:60px;">
                                  </span>
                                  <label class="radio-inline" style="margin-left:20px;">
                                      <input type="radio" name="IsNonEmployee" id="IsNonEmployeeNo" value="否" style="vertical-align: middle;"> 否
                                  </label>
                                  <script type="text/javascript">
                                      $(function () {
                                          $('input[name="IsNonEmployee"]').change(function () {
                                              if ($('#IsNonEmployeeYes').is(':checked')) {
                                                  $('#NonEmployeeDetails').show();
                                              } else {
                                                  $('#NonEmployeeDetails').hide();
                                                  $('#NonEmployeeUnit').val('');
                                                  $('#CooperationRatio1').val('');
                                                  $('#CooperationRatio2').val('');
                                              }
                                          });
                                      });
                                  </script>

                            </div>
                                                   
                            <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                                <label >各發明人貢獻比率總合為100%</label>
                                <table id="tblInventors" class="table table-bordered table-hover">
                                    <thead>
                                        <tr style="background-color: #337ab7; color: white;">
                                            <th style="padding: 8px; text-align: center;">操作</th>
                                            <th style="padding: 12px; text-align: center;">排序</th>
                                            <th style="padding: 12px; text-align: center;">中文姓名</th>
                                            <th style="padding: 12px; text-align: center;">英文姓名</th>
                                            <th style="padding: 12px; text-align: center;">身分證字號</th>
                                            <th style="padding: 12px; text-align: center;">所屬單位</th>
                                            <th style="padding: 12px; text-align: center;">電子郵件</th>
                                            <th style="padding: 12px; text-align: center;">戶籍地址</th>
                                            <th style="padding: 12px; text-align: center;">貢獻比率(%)</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tblInventorsBody">
                                        <tr>
                                            <td>
                                                <input type="button" class="btn btn-warning btn-sm btnEditInventorRow" value="編輯" />
                                                <input type="button" class="btn btn-danger btn-sm btnDeleteInventorRow" value="刪除" />
                                            </td>
                                            <td><input type="text" class="form-control" name="Order" style="width:60px;" /></td>
                                            <td><input type="text" class="form-control" name="ChineseName" /></td>
                                            <td><input type="text" class="form-control" name="EnglishName" /></td>
                                            <td><input type="text" class="form-control" name="IdNumber" /></td>
                                            <td><input type="text" class="form-control" name="Department" /></td>
                                            <td><input type="email" class="form-control" name="Email" /></td>
                                            <td><input type="text" class="form-control" name="Address" /></td>
                                            <td><input type="number" class="form-control" name="Contribution" min="0" max="100" step="1" /></td>
                                        </tr>
                                    </tbody>
                                </table>
                                <button type="button" id="btnAddInventorRow" class="btn btn-primary btn-sm" style="margin-top:10px;">新增一行</button>
                            </div>
                            <script type="text/javascript">
                                $(document).ready(function () {
                                    // 新增一行
                                    $('#btnAddInventorRow').click(function () {
                                        var newRow = `<tr>
                                            <td>
                                                <input type="button" class="btn btn-warning btn-sm btnEditInventorRow" value="編輯" />
                                                <input type="button" class="btn btn-danger btn-sm btnDeleteInventorRow" value="刪除" />
                                            </td>
                                            <td><input type="text" class="form-control" name="Order" style="width:60px;" /></td>
                                            <td><input type="text" class="form-control" name="ChineseName" /></td>
                                            <td><input type="text" class="form-control" name="EnglishName" /></td>
                                            <td><input type="text" class="form-control" name="IdNumber" /></td>
                                            <td><input type="text" class="form-control" name="Department" /></td>
                                            <td><input type="email" class="form-control" name="Email" /></td>
                                            <td><input type="text" class="form-control" name="Address" /></td>
                                            <td><input type="number" class="form-control" name="Contribution" min="0" max="100" step="1" /></td>
                                        </tr>`;
                                        $('#tblInventorsBody').append(newRow);
                                    });

                                    // 刪除一行
                                    $('#tblInventors').on('click', '.btnDeleteInventorRow', function () {
                                        $(this).closest('tr').remove();
                                    });

                                    // 編輯功能可根據需求擴充
                                });
                            </script>
                        </div>
                    </div>
                                                            
                    <div class="panel panel-default">
                        <div class="panel-heading text-left">
                            欲申請專利類別與所要申請的國家及理由
                        </div>
                        <div class="panel-body" style="border-bottom:1px solid black; text-align:left;">
                            <div class="form-group" style="margin-bottom: 10px;">
                                <label style="display: inline-block; width: 100px;">專利類別：</label>
                                <label class="checkbox-inline" style="margin-right: 20px;">
                                    <input type="checkbox" name="PatType" value="PN01" /> 發明
                                </label>
                                <label class="checkbox-inline" style="margin-right: 20px;">
                                    <input type="checkbox" name="PatType" value="PN02" /> 新型
                                </label>
                                <label class="checkbox-inline" style="margin-right: 20px;">
                                    <input type="checkbox" name="PatType" value="PN03" /> 設計
                                </label>
                                <label class="checkbox-inline" style="margin-right: 20px;">
                                    <input type="checkbox" name="PatType" value="PN04" /> 臨時案(需於專利申請日起1年內轉正式案)
                                </label>
                            </div>
                            <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                                <table id="tblPatentApplyCountry" class="table table-bordered table-hover">
                                    <thead>
                                        <tr style="background-color: #337ab7; color: white;">
                                            <th style="padding: 8px; text-align: center;">操作</th>
                                            <th style="padding: 12px; text-align: center;">申請順序</th>
                                            <th style="padding: 12px; text-align: center;">申請國家</th>
                                            <th style="padding: 12px; text-align: center;">申請理由</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tblPatentApplyCountryBody">
                                        <tr>
                                            <td>
                                                <input type="button" class="btn btn-danger btn-sm btnDeletePatentApplyCountryRow" value="刪除" />
                                            </td>
                                            <td>
                                                <input type="text" class="form-control" name="ApplyOrder" style="width:60px;" />
                                            </td>
                                            <td>
                                                <input type="text" class="form-control" name="ApplyCountry" placeholder="如：台灣/美國/日本" />
                                            </td>
                                            <td>
                                                <input type="text" class="form-control" name="ApplyReason" placeholder="請輸入申請理由" />
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <button type="button" id="btnAddPatentApplyCountryRow" class="btn btn-primary btn-sm" style="margin-top:10px;">新增一行</button>
                            </div>


                        </div>
                        <script type="text/javascript">
                            $(document).ready(function () {
                                // 新增一行
                                $('#btnAddPatentApplyCountryRow').click(function () {
                                    var newRow = `<tr>
                                                    <td>
                                                        <input type="button" class="btn btn-danger btn-sm btnDeletePatentApplyCountryRow" value="刪除" />
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control" name="ApplyOrder" style="width:60px;" />
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control" name="ApplyCountry" placeholder="如：台灣/美國/日本" />
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control" name="ApplyReason" placeholder="請輸入申請理由" />
                                                    </td>
                                                </tr>`;
                                    $('#tblPatentApplyCountryBody').append(newRow);
                                });

                                // 刪除一行
                                $('#tblPatentApplyCountry').on('click', '.btnDeletePatentApplyCountryRow', function () {
                                    $(this).closest('tr').remove();
                                });
                            });
                        </script>
                    </div>   
                    
                    <div class="panel panel-default">
                        <div class="panel-heading text-left">
                            本申請案發明內容是否已公開？
                        </div>
                        <div class="panel-body" style="border-bottom:1px solid black; text-align:left;">
                            <div style="margin-bottom: 10px;">
                                為維持申請智慧財產內容之新穎性，請勿在申請前發表相關內容之論文。若已先行發表，請檢附已發表之文獻，並詳細註明其發表日期及與智慧財產內容之相關性。
                            </div>
                            <div style="margin-bottom: 10px;">
                                <label>
                                    <input type="radio" name="IsPublished" value="Yes" /> 是，請註明發表的時間及公開方式(需檢附資料)：
                                </label>
                                <div id="publishedDetail" style="margin-left: 25px; margin-top: 10px; display: none;">
                                    <div>
                                        1. 發表形式：
                                        <label style="margin-right:10px;"><input type="checkbox" name="PublishType" value="學術刊物" />學術刊物</label>
                                        <label style="margin-right:10px;"><input type="checkbox" name="PublishType" value="學術研討會發表" />學術研討會發表</label>
                                        <label style="margin-right:10px;"><input type="checkbox" name="PublishType" value="展覽" />展覽</label>
                                        <label style="margin-right:10px;"><input type="checkbox" name="PublishType" value="其他" id="chkPublishTypeOther" />其他</label>
                                        <input type="text" id="PublishTypeOther" name="PublishTypeOther" placeholder="請輸入其他發表形式" style="width:auto; display:none; margin-left: 5px;" />
                                    </div>
                                    <div style="margin-top:8px;">
                                        2. 發表日期：<input type="text" name="PublishDate" class="txDatepicker" style="width:200px;" />
                                    </div>
                                    <div style="margin-top:8px;">
                                        3. 刊物(發表會)名稱：<input type="text" name="PublishName" style="width:300px;" />
                                    </div>
                                    <div style="margin-top:8px;">
                                        4. 發表地區、所在刊物卷、頁數：<input type="text" name="PublishLocation" style="width:350px;" />
                                    </div>
                                    <div style="margin-top:8px;">
                                        <label>上傳已發表之文獻：</label>
                                        <input type="file" name="PublishFile" />
                                    </div>
                                </div>
                            </div>
                            <div style="margin-bottom: 10px;">
                                <label>
                                    <input type="radio" name="IsPublished" value="No" /> 否，請註明是否有預計公開日期：
                                </label>
                                <input type="text" name="PlanPublishDate" class="txDatepicker" style="width:200px; margin-left:10px;" disabled />
                            </div>
                            <div style="margin-bottom: 10px;">
                                申請人保證上述資料正確無誤，倘有不實，願受法律之懲罰。
                            </div>
                            <div style="margin-bottom: 10px;">
                                <label>發明人代表簽章：</label>
                                <a href="~/Content/範例簽名檔.pdf" download style="margin-left:10px; margin-right:10px;">下載簽名範例</a>
                                <input type="file" name="InventorSignature" accept="image/*,.pdf" />
                            </div>
                        </div>
                        <script type="text/javascript">
                            $(function () {
                                // 顯示/隱藏發表細節
                                $('input[name="IsPublished"]').change(function () {
                                    if ($(this).val() === "Yes") {
                                        $('#publishedDetail').show();
                                        $('input[name="PlanPublishDate"]').prop('disabled', true).val('');
                                    } else {
                                        $('#publishedDetail').hide();
                                        $('input[name="PlanPublishDate"]').prop('disabled', false);
                                    }
                                });
                                // 其他發表形式顯示
                                $('#chkPublishTypeOther').change(function () {
                                    if ($(this).is(':checked')) {
                                        $('#PublishTypeOther').show();
                                    } else {
                                        $('#PublishTypeOther').hide().val('');
                                    }
                                });
                            });
                        </script>
                    </div>
                    


                    <div class="panel panel-default">
                        <div class="panel-heading text-left">
                            請提供您研發成果相關的先前研發成果或技術所做的調查情形
                        </div>
                    
                        <div class="panel-body" style="border-bottom:1px solid black; text-align:left;">
                            <div class="form-group" style="margin-bottom: 15px;">
                                <label for="SearchedPatentData">1. 已檢索之專利資料及技術和文獻：</label>
                                <textarea id="SearchedPatentData" name="SearchedPatentData" class="form-control" rows="3" placeholder="請輸入已檢索之專利資料及技術和文獻"></textarea>
                            </div>
                            <div class="form-group" style="margin-bottom: 15px;">
                                <label for="ImprovementSuggestion">2. 您認為先前相關的研發成果可以改進的缺失：</label>
                                <textarea id="ImprovementSuggestion" name="ImprovementSuggestion" class="form-control" rows="3" placeholder="請輸入可改進的缺失"></textarea>
                            </div>
                            <div class="form-group" style="margin-bottom: 15px;">
                                <label for="SearchKeywords">3. 本案檢索關鍵字：</label>
                                <textarea id="SearchKeywords" name="SearchKeywords" class="form-control" rows="2" placeholder="請輸入本案檢索關鍵字"></textarea>
                            </div>
                        </div>
                    </div>


                    <div class="panel panel-default">
                        <div class="panel-heading text-left">
                            本申請案件的創作目的及優點
                        </div>
                    
                        <div class="panel-body" style="border-bottom:1px solid black; text-align:left;">
                            <div class="form-group" style="margin-bottom: 15px;">
                                <label for="CreationPurpose">1. 本申請案件的創作目的：</label>
                                <textarea id="CreationPurpose" name="CreationPurpose" class="form-control" rows="3" placeholder="請輸入本申請案件的創作目的"></textarea>
                            </div>
                            <div class="form-group" style="margin-bottom: 15px;">
                                <label for="FeatureAndBreakthrough">2. 本申請案件的特色及其突破先前其他研發成果的地方：</label>

                                <textarea id="FeatureAndBreakthrough" name="FeatureAndBreakthrough" class="form-control" rows="5" placeholder="請列出本研發產品在方法/構造/裝置/形狀/成份/組成上與已知技術的比較，尤其是針對其創新、進步或功效等獨特之處，得以行使排除他人侵權及請求賠償損害之專有權利。"></textarea>
                            </div>
                        </div>
                    </div>


                    <div class="panel panel-default">
                        <div class="panel-heading text-left">
                            本申請案所欲保護的智慧財產範圍：（以簡明文字說明本研發產品之內容特點。）
                        </div>
                        <div class="panel-body" style="border-bottom:1px solid black; text-align:left;">
                            <div class="form-group" style="margin-bottom: 15px;">
                                <label for="PropertyProtection">1. 本申請案所欲保護的智慧財產範圍：</label>
                                <textarea id="PropertyProtection" name="PropertyProtection" class="form-control" rows="3" placeholder="請輸入本申請案所欲保護的智慧財產範圍"></textarea>
                            </div>
                        </div>
                    </div>


                    <div class="panel panel-default">
                        <div class="panel-heading text-left">
                            中文摘要及英文摘要
                        </div>
                        <div class="panel-body" style="border-bottom:1px solid black; text-align:left;">
                        
                            <div class="form-group" style="margin-bottom: 15px;">
                                <label for="Abstract">1. 中文摘要：</label>
                                <textarea id="Abstract" name="Abstract" class="form-control" rows="3" placeholder="請輸入中文摘要"></textarea>
                            </div>
                            <div class="form-group" style="margin-bottom: 15px;">
                                <label for="AbstractEn">2. 英文摘要：</label>
                                <textarea id="AbstractEn" name="AbstractEn" class="form-control" rows="3" placeholder="請輸入英文摘要"></textarea>
                            </div>
                        </div>
                    </div>


                    <div class="panel panel-default">
                        <div class="panel-heading text-left">
                            專利圖示附件：
                        </div>

                        
                        <div class="panel-body" style="border-bottom:1px solid black; text-align:left;">
                            <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                                <table id="tblPatentFigures" class="table table-bordered table-hover">
                                    <thead>
                                        <tr style="background-color: #337ab7; color: white;">
                                            <th style="padding: 8px; text-align: center; width: 20%;">操作</th>
                                            <th style="padding: 12px; text-align: center; width: 30%;">圖示</th>
                                            <th style="padding: 12px; text-align: center; width: 50%;">圖示說明</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tblPatentFiguresBody">
                                        <tr>
                                            <td style="text-align: center;">
                                                <input type="file" class="form-control-file" name="FigureImage" accept="image/*" style="display:inline-block; width:auto;" />
                                                <button type="button" class="btn btn-danger btn-sm btnDeleteFigureRow" style="margin-top:5px;">刪除</button>
                                            </td>
                                            <td style="text-align: center;">
                                                <img src="" alt="圖示預覽" class="figure-preview" style="max-width:100px; max-height:100px; display:none;" />
                                            </td>
                                            <td>
                                                <input type="text" class="form-control" name="FigureDescription" placeholder="請輸入圖示說明" />
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <button type="button" id="btnAddFigureRow" class="btn btn-primary btn-sm" style="margin-top:10px;">新增一行</button>
                            </div>
                            <script type="text/javascript">
                                $(document).ready(function () {
                                    // 新增一行
                                    $('#btnAddFigureRow').click(function () {
                                        var newRow = `<tr>
                                            <td style="text-align: center;">
                                                <input type="file" class="form-control-file" name="FigureImage" accept="image/*" style="display:inline-block; width:auto;" />
                                                <button type="button" class="btn btn-danger btn-sm btnDeleteFigureRow" style="margin-top:5px;">刪除</button>
                                            </td>
                                            <td style="text-align: center;">
                                                <img src="" alt="圖示預覽" class="figure-preview" style="max-width:100px; max-height:100px; display:none;" />
                                            </td>
                                            <td>
                                                <input type="text" class="form-control" name="FigureDescription" placeholder="請輸入圖示說明" />
                                            </td>
                                        </tr>`;
                                        $('#tblPatentFiguresBody').append(newRow);
                                    });

                                    // 刪除一行
                                    $('#tblPatentFigures').on('click', '.btnDeleteFigureRow', function () {
                                        $(this).closest('tr').remove();
                                    });

                                    // 預覽圖片
                                    $('#tblPatentFigures').on('change', 'input[type="file"][name="FigureImage"]', function (e) {
                                        var input = this;
                                        var $img = $(this).closest('tr').find('.figure-preview');
                                        if (input.files && input.files[0]) {
                                            var reader = new FileReader();
                                            reader.onload = function (ev) {
                                                $img.attr('src', ev.target.result);
                                                $img.show();
                                            }
                                            reader.readAsDataURL(input.files[0]);
                                        } else {
                                            $img.hide();
                                            $img.attr('src', '');
                                        }
                                    });
                                });
                            </script>
                        </div>
                    </div>


                    <div class="panel panel-default">
                        <div class="panel-heading text-left">
                            本申請案件可能應用的範圍
                        </div>
                        <div class="panel-body" style="border-bottom:1px solid black; text-align:left;">
                            <div class="form-group" style="margin-bottom: 10px;">
                                <label for="FutureIndustryField">1. 未來可能應用之產業領域：</label>
                                <textarea id="FutureIndustryField" name="FutureIndustryField" class="form-control" rows="2" placeholder="請輸入未來可能應用之產業領域"></textarea>
                            </div>
                            <div class="form-group" style="margin-bottom: 10px;">
                                <label for="FutureProduct">2. 未來可能開發或應用之產品：</label>
                                <textarea id="FutureProduct" name="FutureProduct" class="form-control" rows="2" placeholder="請輸入未來可能開發或應用之產品"></textarea>
                            </div>
                            <div class="form-group" style="margin-bottom: 10px;">
                                <label for="FutureLicenseCompany">3. 未來可能授權的公司：<br /><span style="font-weight:normal;">(若無則請提供適合應用本案之公司名稱)</span></label>
                                <textarea id="FutureLicenseCompany" name="FutureLicenseCompany" class="form-control" rows="2" placeholder="請輸入未來可能授權或適合應用本案之公司名稱"></textarea>
                            </div>
                        </div>
                    </div>


                    <div class="panel panel-default">
                        <div class="panel-heading text-left">
                            本申請案之詳細說明
                        </div>
                        <div class="panel-body" style="border-bottom:1px solid black; text-align:left;">
                            <textarea id="DetailedDescription" name="DetailedDescription" class="form-control" rows="3" placeholder="請輸入本申請案之詳細說明"></textarea>
                        
                        
                        </div>
                    </div>


                    <div class="panel panel-default">
                        <div class="panel-heading text-left">
                            本申請案之較佳實施例說明：（舉例說明本發明較佳的具體實施方式。）
                        </div>
                        <div class="panel-body" style="border-bottom:1px solid black; text-align:left;">
                            <textarea id="BestExampleDescription" name="BestExampleDescription" class="form-control" rows="3" placeholder="請輸入本申請案之較佳實施例說明"></textarea>
                        
                        </div>
                    </div>


                    <div class="panel panel-default">
                        <div class="panel-heading text-left">
                            推薦事務所
                        </div>
                

                        <div class="panel-body" style="border-bottom:1px solid black; text-align:left;">
                            <div class="form-group" style="margin-bottom: 10px;">
                                <label>
                                    <input type="radio" name="IsRecommendFirm" value="Y" checked="checked" /> 是
                                </label>
                                &nbsp;&nbsp;&nbsp;&nbsp;
                                <label>
                                    <input type="radio" name="IsRecommendFirm" value="N" /> 否，由本室進行委派
                                </label>
                            </div>
                            <div id="recommendFirmSection">
                                <table class="table table-bordered table-hover" id="tblRecommendFirm">
                                    <thead>
                                        <tr style="background-color: #337ab7; color: white;">
                                            <th style="padding: 8px; text-align: center;">操作</th>
                                            <th style="padding: 12px; text-align: center;">優先序號</th>
                                            <th style="padding: 12px; text-align: center;">事務所名稱</th>
                                            <th style="padding: 12px; text-align: center;">事務所聯絡人</th>
                                            <th style="padding: 12px; text-align: center;">事務所電話/E-mail</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tblRecommendFirmBody">
                                        <tr>
                                            <td>
                                                <input type="button" class="btn btn-danger btn-sm btnDeleteFirmRow" value="刪除" />
                                            </td>
                                            <td>
                                                <input type="text" class="form-control" name="FirmPriorityOrder" style="width:60px;" />
                                            </td>
                                            <td>
                                                <input type="text" class="form-control" name="FirmName" />
                                            </td>
                                            <td>
                                                <input type="text" class="form-control" name="FirmContactPerson" />
                                            </td>
                                            <td>
                                                <input type="text" class="form-control" name="FirmContactInfo" placeholder="電話/E-mail" />
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <button type="button" id="btnAddFirmRow" class="btn btn-primary btn-sm" style="margin-top:10px;">新增一行</button>
                            </div>
                        </div>
                        <script type="text/javascript">
                            $(document).ready(function () {
                                // 切換推薦事務所區塊顯示
                                $('input[name="IsRecommendFirm"]').change(function () {
                                    if ($(this).val() === "Y") {
                                        $('#recommendFirmSection').show();
                                    } else {
                                        $('#recommendFirmSection').hide();
                                    }
                                });
                                // 預設顯示
                                if ($('input[name="IsRecommendFirm"]:checked').val() === "Y") {
                                    $('#recommendFirmSection').show();
                                } else {
                                    $('#recommendFirmSection').hide();
                                }

                                // 新增一行
                                $('#btnAddFirmRow').click(function () {
                                    var newRow = `<tr>
                                        <td>
                                            <input type="button" class="btn btn-danger btn-sm btnDeleteFirmRow" value="刪除" />
                                        </td>
                                        <td>
                                            <input type="text" class="form-control" name="FirmPriorityOrder" style="width:60px;" />
                                        </td>
                                        <td>
                                            <input type="text" class="form-control" name="FirmName" />
                                        </td>
                                        <td>
                                            <input type="text" class="form-control" name="FirmContactPerson" />
                                        </td>
                                        <td>
                                            <input type="text" class="form-control" name="FirmContactInfo" placeholder="電話/E-mail" />
                                        </td>
                                    </tr>`;
                                    $('#tblRecommendFirmBody').append(newRow);
                                });

                                // 刪除一行
                                $('#tblRecommendFirm').on('click', '.btnDeleteFirmRow', function () {
                                    $(this).closest('tr').remove();
                                });
                            });
                        </script>
                    </div>
                <!--
                    申請書下載與上傳區塊
                    1. 下載申請書：點擊按鈕可下載PDF或Word檔（由後端產生）
                    2. 上傳已簽名申請書：可上傳PDF或圖片檔
                -->
                <div class="panel panel-default">
                    <div class="panel-heading text-left">
                        申請書下載與上傳
                    </div>
                    <div class="panel-body" style="border-bottom:1px solid black; text-align:left;">
                        <div class="form-group" style="margin-bottom: 10px;">
                       
                            <label style="color: red;">發明人同意上述發明在專利申請國家之權利，專利權人為國防醫學大學。</label>
                            <label>請下載申請書，列印後由發明人簽名，並於下方上傳已簽名之申請書：</label>
                            
                            <br />
                            <button type="button" id="btnDownloadApplicationForm" class="btn btn-success">
                                下載申請書
                            </button>
                        </div>
                        <div class="form-group" style="margin-bottom: 10px;">
                            <label for="SignedApplicationForm">上傳已簽名申請書：</label>
                            <input type="file" id="SignedApplicationForm" name="SignedApplicationForm" class="form-control-file" accept=".pdf,image/*" />
                            <span class="text-muted" style="font-size:12px;">(請上傳PDF或圖片檔，限單一檔案)</span>
                        </div>
                        <div id="SignedApplicationFormPreview" style="margin-top:10px;"></div>
                    <!--
                        註：另請發明人檢附貢獻比例表及論文或其他相關資料，需由下方上傳。
                    -->
                        <div class="form-group" style="margin-bottom: 10px;">
                            <label for="ContributionTableFile">上傳貢獻比例表：</label>
                            <input type="file" id="ContributionTableFile" name="ContributionTableFile" class="form-control-file" accept=".pdf,.doc,.docx,.xls,.xlsx,image/*" />
                            <span class="text-muted" style="font-size:12px;">(請上傳PDF、Word、Excel或圖片檔，限單一檔案)</span>
                        </div>
                        <div class="form-group" style="margin-bottom: 10px;">
                            <label for="PaperOrOtherFile">上傳論文或其他相關資料：</label>
                            <input type="file" id="PaperOrOtherFile" name="PaperOrOtherFile" class="form-control-file" accept=".pdf,.doc,.docx,.xls,.xlsx,image/*" multiple />
                            <span class="text-muted" style="font-size:12px;">(可上傳多個PDF、Word、Excel或圖片檔)</span>
                        </div>
                        <div id="ContributionTableFilePreview" style="margin-top:10px;"></div>
                        <div id="PaperOrOtherFilePreview" style="margin-top:10px;"></div>
              
                    
                    
                    
                    
                    
                    </div>
                </div>
                
                
                    @*備註：
                    1. 下載申請書功能需後端提供產生PDF/Word的Action（如DownloadApplicationForm）。
                    2. 上傳已簽名申請書，需在表單提交時一併上傳，後端請處理檔案儲存。
                    3. 若需即時上傳，可改用AJAX上傳，或於表單送出時一併處理。*@
                


  

                       




                   


                </div>
            </div>

            @*第二個頁籤：推薦事務所*@
            <div id="dv_prop_firm" class="row b2k-control" style="text-align:center; border-radius:10px 10px; padding-top:5px; padding-bottom:5px; background-color:#dddcdc; margin:5px; max-height:calc(100vh - 200px); overflow-y:auto; min-height:400px;">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="width:99%; font-weight:800; color:black; text-align:center; vertical-align:middle; border-radius:10px 10px; padding-top:5px; padding-bottom:5px; background-color:#dddcdc; margin:15px;">

                    @*推薦事務所*@
                    <div class="panel panel-default" >
                        <div class="panel-heading text-left" >推薦事務所
                        </div>                                                           
                                     
                        <div class="panel-body" style="border-bottom:1px solid black; text-align:left;">
                        
                         <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                            <table id="tblFirms" class="table table-bordered table-hover">
                                <thead>
                                    <tr style="background-color: #337ab7; color: white;">
                                        <th style="padding: 12px; text-align: center;">操作</th>
                                        <th style="padding: 12px; text-align: center;">序號</th>
                                        <th style="padding: 12px; text-align: center;">事務所名稱</th>
                                        <th style="padding: 12px; text-align: center;">事務所聯絡人</th>
                                        <th style="padding: 12px; text-align: center;">事務所電話/E-mail</th>
                                        <th style="padding: 12px; text-align: center;">代理人代碼</th>
                                        <th style="padding: 12px; text-align: center;">國籍</th>
                                        <th style="padding: 12px; text-align: center;">代理人姓名</th>
                                        <th style="padding: 12px; text-align: center;">承辦工程師</th>
                                        <th style="padding: 12px; text-align: center;">提交說明</th>
                                    </tr>
                                </thead>
                                <tbody id="tblFirmsBody">
                                    <tr>
                                        <td>
                                            <input type="button" class="btn btn-warning btn-sm btnEditFirmRow" value="編輯" />
                                            <input type="button" class="btn btn-danger btn-sm btnDeleteFirmRow" value="刪除" />
                                        </td>
                                        <td><input type="text" class="form-control" name="FirmOrder" style="width:60px;" /></td>
                                        <td><input type="text" class="form-control" name="FirmName" /></td>
                                        <td><input type="text" class="form-control" name="FirmContact" /></td>
                                        <td><input type="text" class="form-control" name="FirmPhoneEmail" /></td>
                                        <td><input type="text" class="form-control" name="AgentCode" /></td>
                                        <td><input type="text" class="form-control" name="Nationality" /></td>
                                        <td><input type="text" class="form-control" name="AgentName" /></td>
                                        <td><input type="text" class="form-control" name="Engineer" /></td>
                                        <td><input type="text" class="form-control" name="SubmitNote" /></td>
                                    </tr>
                                </tbody>
                            </table>
                            <button type="button" id="btnAddFirmRow" class="btn btn-primary btn-sm" style="margin-top:10px;">新增事務所</button>
                        </div>
                        <script type="text/javascript">
                            // 注意：如果頁面有多個 jQuery 版本或 js 錯誤，會導致事件無法綁定
                            // 請檢查 console 是否有 js error
                            // 若本區塊在部分 tab 或 ajax 載入，請確保 script 有正確執行
                            // 建議將 script 放到頁面最下方，或用 window.onload 減少衝突
                            $(function () {
                                // 新增一行
                                $('#btnAddFirmRow').off('click').on('click', function () {
                                    var newRow = '<tr>' +
                                        '<td>' +
                                            '<input type="button" class="btn btn-warning btn-sm btnEditFirmRow" value="編輯" />' +
                                            '<input type="button" class="btn btn-danger btn-sm btnDeleteFirmRow" value="刪除" />' +
                                        '</td>' +
                                        '<td><input type="text" class="form-control" name="FirmOrder" style="width:60px;" /></td>' +
                                        '<td><input type="text" class="form-control" name="FirmName" /></td>' +
                                        '<td><input type="text" class="form-control" name="FirmContact" /></td>' +
                                        '<td><input type="text" class="form-control" name="FirmPhoneEmail" /></td>' +
                                        '<td><input type="text" class="form-control" name="AgentCode" /></td>' +
                                        '<td><input type="text" class="form-control" name="Nationality" /></td>' +
                                        '<td><input type="text" class="form-control" name="AgentName" /></td>' +
                                        '<td><input type="text" class="form-control" name="Engineer" /></td>' +
                                        '<td><input type="text" class="form-control" name="SubmitNote" /></td>' +
                                    '</tr>';
                                    $('#tblFirmsBody').append(newRow);
                                });

                                // 刪除一行
                                $('#tblFirms').on('click', '.btnDeleteFirmRow', function () {
                                    $(this).closest('tr').remove();
                                });

                                // 編輯功能可根據需求擴充
                            });
                        </script>
                    </div>
                   
                    
                    
                </div>
            </div>




            @*第三個頁籤：專利技術審查*@
            <div id="dv_prop_tech_review" class="row b2k-control" style="text-align:center; border-radius:10px 10px; padding-top:5px; padding-bottom:5px; background-color:#dddcdc; margin:5px; max-height:calc(100vh - 200px); overflow-y:auto; min-height:400px;">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="width:99%; font-weight:800; color:black; text-align:center; vertical-align:middle; border-radius:10px 10px; padding-top:5px; padding-bottom:5px; background-color:#dddcdc; margin:15px;">


                    @*專利技術審查*@
                    
                    <div class="panel panel-default" >
                        <div class="panel-heading text-left" >
                                    專利技術審查委員
                        </div>
                        
                        <div class="panel-body" style="border-bottom:1px solid black; text-align:center;">
                            <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">                           
                                <table id="tblTechReviewers" class="table table-bordered table-hover">
                                    <thead>
                                        <tr style="background-color: #337ab7; color: white;">
                                            <th style="padding: 8px; text-align: center;">操作</th>
                                            <th style="padding: 12px; text-align: center;">校內/外</th>
                                            <th style="padding: 12px; text-align: center;">姓名</th>
                                            <th style="padding: 12px; text-align: center;">服務單位</th>
                                            <th style="padding: 12px; text-align: center;">系/所</th>
                                            <th style="padding: 12px; text-align: center;">中心</th>
                                            <th style="padding: 12px; text-align: center;">職稱</th>
                                            <th style="padding: 12px; text-align: center;">電話</th>
                                            <th style="padding: 12px; text-align: center;">地址</th>
                                            <th style="padding: 12px; text-align: center;">Email</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tblTechReviewersBody">
                                        <tr>
                                            <td>
                                                <input type="button" class="btn btn-danger btn-sm btnDeleteTechReviewerRow" value="刪除" />
                                            </td>
                                            <td><input type="text" class="form-control" name="InOrOut" /></td>
                                            <td><input type="text" class="form-control" name="Name" /></td>
                                            <td><input type="text" class="form-control" name="ServiceUnit" /></td>
                                            <td><input type="text" class="form-control" name="Department" /></td>
                                            <td><input type="text" class="form-control" name="Center" /></td>
                                            <td><input type="text" class="form-control" name="Title" /></td>
                                            <td><input type="text" class="form-control" name="Phone" /></td>
                                            <td><input type="text" class="form-control" name="Address" /></td>
                                            <td><input type="text" class="form-control" name="Email" /></td>
                                        </tr>
                                    </tbody>
                                </table>
                                <button type="button" id="btnAddTechReviewerRow" class="btn btn-primary btn-sm" style="margin-top:10px;">新增一行</button>
                            </div>
                            <script type="text/javascript">
                                $(document).ready(function () {
                                    $('#btnAddTechReviewerRow').click(function () {
                                        var newRow = `<tr>
                                            <td>
                                                <input type="button" class="btn btn-danger btn-sm btnDeleteTechReviewerRow" value="刪除" />
                                            </td>
                                            <td><input type="text" class="form-control" name="InOrOut" /></td>
                                            <td><input type="text" class="form-control" name="Name" /></td>
                                            <td><input type="text" class="form-control" name="ServiceUnit" /></td>
                                            <td><input type="text" class="form-control" name="Department" /></td>
                                            <td><input type="text" class="form-control" name="Center" /></td>
                                            <td><input type="text" class="form-control" name="Title" /></td>
                                            <td><input type="text" class="form-control" name="Phone" /></td>
                                            <td><input type="text" class="form-control" name="Address" /></td>
                                            <td><input type="text" class="form-control" name="Email" /></td>
                                        </tr>`;
                                        $('#tblTechReviewersBody').append(newRow);
                                    });

                                    $('#tblTechReviewers').on('click', '.btnDeleteTechReviewerRow', function () {
                                        $(this).closest('tr').remove();
                                    });
                                });
                            </script>
                        </div>
                    </div>                
                </div>
            </div>
            @*第四個頁籤：前置檢索*@
            <div id="dv_prop_pre_search" class="row b2k-control" style="text-align:center; border-radius:10px 10px; padding-top:5px; padding-bottom:5px; background-color:#dddcdc; margin:5px; max-height:calc(100vh - 200px); overflow-y:auto; min-height:400px;">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="width:99%; font-weight:800; color:black; text-align:center; vertical-align:middle; border-radius:10px 10px; padding-top:5px; padding-bottom:5px; background-color:#dddcdc; margin:15px;">


                    @*前置檢索*@
                    <h5 style="color: #337ab7; margin-bottom: 15px;">前置檢索</h5>
                    <div class="table-responsive">
                        <table id="tblPreSearch" class="table table-bordered table-hover">
                            <thead>
                                <tr style="background-color: #337ab7; color: white;">

                                    <th style="padding: 8px; text-align: center;">操作</th>
                                    <th style="padding: 12px; text-align: center;">檢索日期</th>
                                    <th style="padding: 12px; text-align: center;">檢索範圍、條件</th>
                                    <th style="padding: 12px; text-align: center;">代理人</th>
                                    <th style="padding: 12px; text-align: center;">判定代碼</th>
                                    <th style="padding: 12px; text-align: center;">上傳附件</th>

                                </tr>
                            </thead>
                            <tbody id="tblPreSearchBody">
                                <!-- 動態新增的前置檢索會插入這裡 -->
                                <tr>
                                    <td><input type="button" class="btn btn-danger btn-sm" value="刪除" /></td>
                                    <td><input type="text" class="form-control" name="SearchDate" /></td>
                                    <td><input type="text" class="form-control" name="SearchScope" /></td>
                                    <td><input type="text" class="form-control" name="Agent" /></td>
                                    <td><input type="text" class="form-control" name="DecisionCode" /></td>
                                    <td><input type="file" class="form-control" name="Attachment" /></td>
                                </tr>
                            </tbody>
                        </table>
                        <button type="button" id="btnAddPreSearchRow" class="btn btn-primary btn-sm" style="margin-top:10px;">新增一行</button>
                    </div>
                    <div id="noDataMessage" style="text-align: center; color: #999; padding: 20px; display: none;">

                    </div>
                </div>
            </div>
            @*第五個頁籤：智審會審核結果*@
            <div id="dv_prop_ip_review" class="row b2k-control" style="text-align:center; border-radius:10px 10px; padding-top:5px; padding-bottom:5px; background-color:#dddcdc; margin:5px; max-height:calc(100vh - 200px); overflow-y:auto; min-height:400px;">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="width:99%; font-weight:800; color:black; text-align:center; vertical-align:middle; border-radius:10px 10px; padding-top:5px; padding-bottom:5px; background-color:#dddcdc; margin:15px;">

                    @*智審會審核結果*@
                    <h5 style="color: #337ab7; margin-bottom: 15px;">智審會審核結果</h5>
                    <div class="table-responsive">
                        <table id="tblIPReview" class="table table-bordered table-hover">
                            <thead>
                                <tr style="background-color: #337ab7; color: white;">

                                    <th style="padding: 8px; text-align: center;">操作</th>
                                    <th style="padding: 12px; text-align: center;">審查人</th>
                                    <th style="padding: 12px; text-align: center;">是否具有產業利用性</th>
                                    <th style="padding: 12px; text-align: center;">是否具有新穎性</th>
                                    <th style="padding: 12px; text-align: center;">是否只有進步性</th>
                                    <th style="padding: 12px; text-align: center;">申請範圍是否合適</th>
                                    <th style="padding: 12px; text-align: center;">申請國家</th>
                                    <th style="padding: 12px; text-align: center;">該專利相互授權可能性</th>
                                    <th style="padding: 12px; text-align: center;">該專利進入市場發展的可行性</th>
                                    <th style="padding: 12px; text-align: center;">該專利技術之競爭性</th>
                                    <th style="padding: 12px; text-align: center;">審查結果</th>
                                    <th style="padding: 12px; text-align: center;">審查建議&補出資料</th>
                                    <th style="padding: 12px; text-align: center;">審查完成日</th>

                                </tr>
                            </thead>
                            <tbody id="tblIPReviewBody">
                                <!-- 動態新增的智審會審核結果會插入這裡 -->
                                <tr>
                                    <td><input type="button" class="btn btn-danger btn-sm" value="刪除" /></td>
                                    <td><input type="text" class="form-control" name="Reviewer" /></td>
                                    <td><input type="text" class="form-control" name="IndustryUse" /></td>
                                    <td><input type="text" class="form-control" name="Novelty" /></td>
                                    <td><input type="text" class="form-control" name="Inventiveness" /></td>
                                    <td><input type="text" class="form-control" name="ScopeAppropriate" /></td>
                                    <td><input type="text" class="form-control" name="ApplyCountry" /></td>
                                    <td><input type="text" class="form-control" name="CrossLicensePossibility" /></td>
                                    <td><input type="text" class="form-control" name="MarketFeasibility" /></td>
                                    <td><input type="text" class="form-control" name="Competitiveness" /></td>
                                    <td><input type="text" class="form-control" name="ReviewResult" /></td>
                                    <td><input type="text" class="form-control" name="ReviewSuggestion" /></td>
                                    <td><input type="text" class="form-control" name="ReviewDate" /></td>
                                </tr>
                            </tbody>
                        </table>
                        <button type="button" id="btnAddIPReviewRow" class="btn btn-primary btn-sm" style="margin-top:10px;">新增一行</button>
                    </div>
                    <div id="noDataMessage" style="text-align: center; color: #999; padding: 20px; display: none;">

                    </div>
                </div>
            </div>
            @*第六個頁籤：提案歷程*@
            <!--<div id="dv_prop_process" class="row b2k-control" style="text-align:center;  border-radius:10px 10px; padding-top:5px; padding-bottom:5px; background-color:#dddcdc; margin:5px; ">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="width:99%; font-weight:800; color:black; text-align:center; vertical-align:middle; border-radius:10px 10px; padding-top:5px; padding-bottom:5px; background-color:#dddcdc; margin:15px;overflow-y:auto;">-->

                    @*提案歷程*@
                    <!--<h5 style="color: #337ab7; margin-bottom: 15px;">提案歷程</h5>
                    <div class="table-responsive">
                        <table id="tblChargeStandards" class="table table-bordered table-hover">
                            <thead>
                                <tr style="background-color: #337ab7; color: white;">

                                    <th style="padding: 8px; text-align: center;">操作</th>
                                    <th style="padding: 12px; text-align: center;">時間</th>
                                    <th style="padding: 12px; text-align: center;">狀態</th>
                                    <th style="padding: 12px; text-align: center;">狀態說明</th>
                                    <th style="padding: 12px; text-align: center;">上傳附件</th>
                                    <th style="padding: 12px; text-align: center;">記錄人</th>

                                </tr>
                            </thead>
                            <tbody id="tblChargeStandardsBody">-->
                                <!-- 動態新增的提案歷程會插入這裡 -->
                                <!--<tr>
                                    <td style="text-align: center;">
                                        <button type="button" class="btn btn-danger btn-sm">刪除</button>
                                    </td>
                                    <td><input type="text" class="form-control" placeholder="請輸入時間" /></td>
                                    <td><input type="text" class="form-control" placeholder="請輸入狀態" /></td>
                                    <td><input type="text" class="form-control" placeholder="請輸入狀態說明" /></td>
                                    <td><input type="file" class="form-control" /></td>
                                    <td><input type="text" class="form-control" placeholder="請輸入記錄人" /></td>
                                </tr>
                            </tbody>
                        </table>
                        <button type="button" id="btnAddChargeStandardRow" class="btn btn-primary btn-sm" style="margin-top:10px;">新增一行</button>
                    </div>
                    <div id="noDataMessage" style="text-align: center; color: #999; padding: 20px; display: none;">

                    </div>
                </div>
            </div>-->



        </div>


    </div>
    <!-- 設定專利管理編號對話框 -->
    <div id="MyCustomDialog" class="b2k-control" style="display:none;">
        <table class="table table-bordered" id="tblPatManageNo">
            <thead>
                <tr>
                    <th style="text-align:center;">是否設定</th>
                    <th style="text-align:center;">國家</th>
                    <th style="text-align:center;">專利管理編號</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td style="text-align:center;">
                        <input type="checkbox" id="chkPatManageNo" />
                    </td>
                    <td>
                        <select id="selPatManageNoCountry" class="form-control">
                            <option value="">代碼維護可設定國家</option>
                            <option value="TW">台灣</option>
                            <option value="CN">中國</option>
                            <option value="US">美國</option>
                            <option value="JP">日本</option>
                            <option value="KR">韓國</option>    
                            <option value="EU">歐盟</option>                      
                       </select>
                    </td>
                    <td>
                        <input type="text" id="txtPatManageNo" class="form-control" placeholder="設定專利管理編號" />
                    </td>
                </tr>
            </tbody>
        </table>




    </div>
    
    
    
    <div class="View2 wrap-login100 p-b-30" style="display:none;">
    </div>


</div>






<script type="text/javascript">
    (function ($) {
        $(function () {
            var sswindow_height = $(window).height(); //整個瀏覽器視窗的高度
            var ss_Header_height = $('.limiter').height(); //__Header.vbhtml的高度
            var ssView1_height = (sswindow_height - ss_Header_height); //View1的高度
            var ssfixed1_height = $('#@id #divfixed1').height(); //除了資料清單表資料(功能名稱、注意事項、....)的高度
            var ssNfixed1_height = ssView1_height - ssfixed1_height; //資料清單表資料的高度
            var ssbFilter_height = 31; //搜尋高度:固定高度無須調整
            var ssInfoEmpty_height = 20; //顯示筆數高度(顯示第 0 至 0 項結果，共 0 項):固定高度無須調整
            var ssTblheader_height = 90; //資料清單表頭高度:不固定請自行調整(當表頭越高，值越大，相反的，當表頭越低，值越小)
            var ssTbl; //資料清單表

            //-----畫面執行-----
            CreateTbl($('#@id #Tbl'), true, (ssNfixed1_height - ssbFilter_height - ssInfoEmpty_height - ssTblheader_height));

            // 初始化頁籤功能
            $('#@id #tab_Apply').tabs();

            // 初始化對話框
            $('#@id #MyCustomDialog').dialog({
                resizable: false,  //寬高不可變動
                autoOpen: false,   //初始不顯示
                title: '設定專利管理編號', //視窗標題名稱
                appendTo: '#@id',
                height: "auto", //高度
                width: 500, //寬度
                modal: true,
                buttons: [
                    {
                        text: "確定",
                        click: function() {
                            $(this).dialog("close");
                        },
                        class: "btn btn-primary"
                    },
                    {
                        text: "取消",
                        click: function() {
                            $(this).dialog("close");
                        },
                        class: "btn btn-default"
                    }
                ]
            });

            //-----欄位格式設定-----
            $('#@id .txDatepicker').datepicker({
                format: "twy/mm/dd",
                weekStart: 1,
                maxViewMode: 1,
                language: "zh-TW"
            });


            //------------------
            //-----觸發事件-----
            //------------------


            //-----click事件-----
            //返回
            $('#@id #btExit').click(function () {
                $(this).trigger('YIC20MainDetail_Close');
            });



            //查詢
            $('#btQry').click(function () {
                QryClick();
            });



            //------------------

            // 完成申請按鈕 - 開啟對話框
            $('#@id #btCompleteApply').click(function () {
                // 清空文字框
                $('#@id #chkPatManageNo').val('');
                $('#@id #selPatManageNoCountry').val('');
                $('#@id #txtPatManageNo').val('');
                // 開啟對話框
                $('#@id #MyCustomDialog').dialog("open");
            });



            //-----欄位格式設定-----
            $('#@id .txDatepicker').datepicker({
                format: "twy/mm/dd",
                weekStart: 1,
                maxViewMode: 1,
                language: "zh-TW"
            });
            //----------------------



            //-----function-----

                        //查詢
            function QryClick() {
                var ttobj = {};
                // 取得查詢條件
                ttobj.DutyOrgID = $('#@id #selResponsibleUnit').val() || '';
                ttobj.Core = $('#@id #selCore').val() || '';
                ttobj.ResID = $('#@id #selInstrumentNumber').val() || '';
                ttobj.Location = $('#@id #selInstrumentLocation').val() || '';
                ttobj.BuyYear = $('#@id #selPurchaseYear').val() || '';
                ttobj.ResName = $('#@id #selChineseInstrumentName').val() || '';
                ttobj.IsRent = $('#@id #selInstrumentStatus').val() || '';
                ttobj.BorrowerID = $('#@id #selUserType').val() || '';
                var urlJSON = '@Url.Action("InstrumentList_Qry", "YIC20")';

                $.ajax({
                    type: 'POST',
                    url: urlJSON,
                    data: {
                        ppqmodel: ttobj
                    },
                    success: function (data) {

                        data.IsOK = true
                        if (data.IsOK) {
                            $('#@id #Tbl').find('tbody').remove();
                            $('#@id #Tbl').append('<tbody>');
                            $.map(data.obj, function (item) {
                                var tr = $('<tr class="ListRow">');
                                $('<td class="tools">')
                                    .append('<button type="button" title="詳細" class="clDetail"><i class="glyphicon glyphicon-th-large"></i></button>')
                                    .appendTo(tr);
                                $('<td headers="objid" class="Colobjid" style="display:none">' + (item.objid || '') + ' </td>').appendTo(tr);
                                $('<td headers="Core" class="ColCore" value="' + item.Core + '">' + (item.Core || '') + '</td>').appendTo(tr);
                                $('<td headers="ResID" class="ColResID" value="' + item.ResID + '">' + (item.ResID || '') + '</td>').appendTo(tr);
                                $('<td headers="Location" class="ColLocation" value="' + item.Location + '">' + (item.Location || '') + '</td>').appendTo(tr);
                                $('<td headers="BuyYear" class="ColBuyYear" value="' + item.BuyYear + '">' + (item.BuyYear || '') + '</td>').appendTo(tr);
                                $('<td headers="ResName" class="ColResName" value="' + item.ResName + '">' + (item.ResName || '') + '</td>').appendTo(tr);
                                $('<td headers="IsRent" class="ColIsRent" value="' + item.IsRent + '">' + (item.IsRent || '') + '</td>').appendTo(tr);
                                $('#@id #Tbl').append(tr);
                            });


                            CreateTbl($('#@id #Tbl'), true, (ssNfixed1_height - ssbFilter_height - ssInfoEmpty_height - ssTblheader_height));
                        } else {
                            alert(data.ErrStr || data.MSG || "查詢失敗");
                        }
                    }
                });
            }




            function CreateTbl(ppTbl, ppbFilter, ppscrollY) {
                ssTbl = ppTbl.DataTable({
                    "bFilter": ppbFilter,  //搜尋
                    "bLengthChange": true,
                    "paging": false,        //分頁
                    "bAutoWidth": false,
                    "fixedHeader": true, //表頭固定
                    "scrollY": ppscrollY, //超過(設定值)時，顯示卷軸
                    /*"scrollX": "auto", //auto*/
                    //固定首列，需要引入相應的dataTables.fixedColumns.min.js
                    //"fixedColumns": {
                    //    "leftColumns": 1 //最左側1列固定
                    //},

                    deferRender: true,
                    destroy: true,
                    /*scroller: true,*/
                    responsive: true, // 修正：改為 true 支援響應式

                    // 新增：欄位定義
                    "columnDefs": [
                        { "orderable": false, "targets": [0, 1] }, // checkbox和操作欄不排序
                        { "className": "text-center", "targets": "_all" } // 所有欄位置中
                    ],

                    // 新增：行创建时的样式设置
                    "createdRow": function(row, data, dataIndex) {
                        $(row).find('td').css({
                            'padding': '1em',
                            'text-align': 'center'
                        });
                    },

                    // 空資料時的顯示訊息
                    "language": {
                        "emptyTable": "暫無設備資料，請點擊查詢按鈕"
                    }
                });
            }

            $(window).resize(function () {
                sswindow_height = $(window).height(); //整個瀏覽器視窗的高度
                ssView1_height = (sswindow_height - ss_Header_height); //View1的高度
                ssNfixed1_height = ssView1_height - ssfixed1_height; //資料清單表資料的高度
                $('#@id .View1').css('height', ssView1_height + 'px');
                $('#@id #divNfixed1').css('height', ssNfixed1_height + 'px');
                CreateTbl($('#@id #Tbl'), true, (ssNfixed1_height - ssbFilter_height - ssInfoEmpty_height - ssTblheader_height));
                ssTbl.columns.adjust();


            });


        });
    })(jQuery);

    // 專利類別複選功能
        function updatePatentTypeDisplay() {
            var selectedTypes = [];
            var checkboxes = document.querySelectorAll('input[name="PatType"]:checked');
            
            checkboxes.forEach(function(checkbox) {
                var label = checkbox.parentNode.textContent.trim();
                selectedTypes.push(label);
            });
            
            // 更新按鈕顯示文字
            var displayText = selectedTypes.length > 0 ? 
                selectedTypes.length + ' 項已選擇' : 
                '請選擇專利類別';
            document.getElementById('PatTypeDisplay').textContent = displayText;
            
            // 更新 input 欄位顯示所選項目
            var inputText = selectedTypes.length > 0 ? 
                selectedTypes.join(', ') : 
                '';
            document.getElementById('PatTypeSelected').value = inputText;
        }

        // 申請國別複選功能
        function updateApplyCountryDisplay() {
            var selectedCountries = [];
            var checkboxes = document.querySelectorAll('input[name="ApplyCountry"]:checked');
            
            checkboxes.forEach(function(checkbox) {
                var label = checkbox.parentNode.textContent.trim();
                selectedCountries.push(label);
            });
            
            // 更新按鈕顯示文字
            var displayText = selectedCountries.length > 0 ? 
                selectedCountries.length + ' 項已選擇' : 
                '請選擇申請國別';
            document.getElementById('ApplyCountryDisplay').textContent = displayText;
            
            // 更新 input 欄位顯示所選項目
            var inputText = selectedCountries.length > 0 ? 
                selectedCountries.join(', ') : 
                '';
            document.getElementById('ApplyCountrySelected').value = inputText;
        }

        // 防止下拉選單在點擊複選框時關閉
        $(document).ready(function() {
            $('#@id .dropdown-menu').on('click', function(e) {
                e.stopPropagation();
            });
        });





    function UploadedFileToFTP_OK(ppReturnShowName, ppFTPFileName) {

            }

        //// 收費標準管理功能
        //var chargeStandards = [];
        //var currentEditIndex = -1;

        //// 新增收費標準
        //$('#btAddCharge').click(function() {
        //    if (validateChargeForm()) {
        //        addChargeStandard();
        //    }
        //});

        //// 修改收費標準
        //$('#btEditCharge').click(function() {
        //    if (currentEditIndex >= 0) {
        //        if (validateChargeForm()) {
        //            updateChargeStandard();
        //        }
        //    } else {
        //        showMessage('請先選擇要修改的收費標準！', 'warning');
        //    }
        //});

        //// 刪除收費標準
        //$('#btDeleteCharge').click(function() {
        //    if (currentEditIndex >= 0) {
        //        deleteChargeStandard();
        //    } else {
        //        showMessage('請先選擇要刪除的收費標準！', 'warning');
        //    }
        //});

        //// 驗證表單
        //function validateChargeForm() {
        //    var isValid = true;
        //    var requiredFields = [
        //        {id: 'BorrowerID', name: '使用者類型'},
        //        {id: 'ChargeItem', name: '收費項目'},
        //        {id: 'TimeSlot', name: '時段'},
        //        {id: 'BillingUnit', name: '計費單位'},
        //        {id: 'Amount', name: '金額'}
        //    ];

        //    // 移除之前的錯誤樣式
        //    requiredFields.forEach(function(field) {
        //        $('#' + field.id).removeClass('error-field');
        //    });

        //    // 檢查必填欄位
        //    var missingFields = [];
        //    requiredFields.forEach(function(field) {
        //        var value = $('#' + field.id).val();
        //        if (!value || value.trim() === '') {
        //            $('#' + field.id).addClass('error-field');
        //            missingFields.push(field.name);
        //            isValid = false;
        //        }
        //    });

        //    if (!isValid) {
        //        showMessage('請填寫以下必填欄位：' + missingFields.join('、'), 'error');
        //    }

        //    return isValid;
        //}

        //// 新增收費標準
        //function addChargeStandard() {
        //    var chargeStandard = {
        //        id: generateId(),
        //        dutyOrgID: $('#DutyOrgID').val(),
        //        core: $('#Core').val(),
        //        resID: $('#ResID').val(),
        //        resName: $('#ResName').val(),
        //        borrowerID: $('#BorrowerID').val(),
        //        chargeItem: $('#ChargeItem').val(),
        //        timeSlot: $('#TimeSlot').val(),
        //        billingUnit: $('#BillingUnit').val(),
        //        amount: parseFloat($('#Amount').val()) || 0,
        //        otherFee: parseFloat($('#OtherFee').val()) || 0,
        //        deposit: parseFloat($('#Deposit').val()) || 0,
        //        overTime: parseFloat($('#OverTime').val()) || 0,
        //        overTimeUnit: $('#OverTimeUnit').val(),
        //        note: $('#Note').val()
        //    };

        //    chargeStandards.push(chargeStandard);
        //    renderChargeStandardsTable();
        //    clearForm();
        //    showMessage('收費標準新增成功！', 'success');
        //}

        //// 更新收費標準
        //function updateChargeStandard() {
        //    var chargeStandard = {
        //        id: chargeStandards[currentEditIndex].id,
        //        dutyOrgID: $('#DutyOrgID').val(),
        //        core: $('#Core').val(),
        //        resID: $('#ResID').val(),
        //        resName: $('#ResName').val(),
        //        borrowerID: $('#BorrowerID').val(),
        //        chargeItem: $('#ChargeItem').val(),
        //        timeSlot: $('#TimeSlot').val(),
        //        billingUnit: $('#BillingUnit').val(),
        //        amount: parseFloat($('#Amount').val()) || 0,
        //        otherFee: parseFloat($('#OtherFee').val()) || 0,
        //        deposit: parseFloat($('#Deposit').val()) || 0,
        //        overTime: parseFloat($('#OverTime').val()) || 0,
        //        overTimeUnit: $('#OverTimeUnit').val(),
        //        note: $('#Note').val()
        //    };

        //    chargeStandards[currentEditIndex] = chargeStandard;
        //    renderChargeStandardsTable();
        //    clearForm();
        //    currentEditIndex = -1;
        //    showMessage('收費標準修改成功！', 'success');
        //}

        //// 刪除收費標準
        //function deleteChargeStandard() {
        //    if (confirm('確定要刪除此收費標準嗎？')) {
        //        chargeStandards.splice(currentEditIndex, 1);
        //        renderChargeStandardsTable();
        //        clearForm();
        //        currentEditIndex = -1;
        //        showMessage('收費標準刪除成功！', 'success');
        //    }
        //}

        //// 渲染收費標準表格
        //function renderChargeStandardsTable() {
        //    var tbody = $('#tblChargeStandardsBody');
        //    tbody.empty();

        //    if (chargeStandards.length === 0) {
        //        $('#noDataMessage').show();
        //        return;
        //    } else {
        //        $('#noDataMessage').hide();
        //    }

        //    chargeStandards.forEach(function(standard, index) {
        //        var row = $('<tr>');
        //        if (index === currentEditIndex) {
        //            row.addClass('warning'); // 高亮選中的行
        //        }

        //        row.append('<td style="text-align: center;">' +
        //                  '<button type="button" class="btn btn-xs btn-info select-btn" data-index="' + index + '" title="選擇">' +
        //                  '<i class="glyphicon glyphicon-hand-up"></i></button></td>');
        //        row.append('<td style="text-align: center;">' + (standard.borrowerID || '') + '</td>');
        //        row.append('<td style="text-align: center;">' + (standard.chargeItem || '') + '</td>');
        //        row.append('<td style="text-align: center;">' + (standard.timeSlot || '') + '</td>');
        //        row.append('<td style="text-align: center;">' + (standard.billingUnit || '') + '</td>');
        //        row.append('<td style="text-align: right;">' + (standard.amount ? standard.amount.toLocaleString() : '0') + '</td>');
        //        row.append('<td style="text-align: right;">' + (standard.otherFee ? standard.otherFee.toLocaleString() : '0') + '</td>');
        //        row.append('<td style="text-align: right;">' + (standard.deposit ? standard.deposit.toLocaleString() : '0') + '</td>');
        //        row.append('<td style="text-align: right;">' + (standard.overTime ? standard.overTime.toLocaleString() : '0') +
        //                  (standard.overTimeUnit ? '/' + standard.overTimeUnit : '') + '</td>');
        //        row.append('<td style="text-align: center;">' + (standard.note || '') + '</td>');

        //        tbody.append(row);
        //    });

        //    // 綁定選擇按鈕事件
        //    $('.select-btn').click(function() {
        //        var index = parseInt($(this).data('index'));
        //        selectChargeStandard(index);
        //    });
        //}

        //// 選擇收費標準
        //function selectChargeStandard(index) {
        //    currentEditIndex = index;
        //    var standard = chargeStandards[index];

        //    // 填入表單
        //    $('#DutyOrgID').val(standard.dutyOrgID || '');
        //    $('#Core').val(standard.core || '');
        //    $('#ResID').val(standard.resID || '');
        //    $('#ResName').val(standard.resName || '');
        //    $('#BorrowerID').val(standard.borrowerID || '');
        //    $('#ChargeItem').val(standard.chargeItem || '');
        //    $('#TimeSlot').val(standard.timeSlot || '');
        //    $('#BillingUnit').val(standard.billingUnit || '');
        //    $('#Amount').val(standard.amount || '');
        //    $('#OtherFee').val(standard.otherFee || '');
        //    $('#Deposit').val(standard.deposit || '');
        //    $('#OverTime').val(standard.overTime || '');
        //    $('#OverTimeUnit').val(standard.overTimeUnit || '');
        //    $('#Note').val(standard.note || '');

        //    // 重新渲染表格以高亮選中的行
        //    renderChargeStandardsTable();
        //    showMessage('已選擇收費標準，可以進行修改或刪除操作', 'info');
        //}

        //// 清空表單
        //function clearForm() {
        //    $('#DutyOrgID').val('');
        //    $('#Core').val('');
        //    $('#ResID').val('');
        //    $('#ResName').val('');
        //    $('#BorrowerID').val('');
        //    $('#ChargeItem').val('');
        //    $('#TimeSlot').val('');
        //    $('#BillingUnit').val('');
        //    $('#Amount').val('');
        //    $('#OtherFee').val('');
        //    $('#Deposit').val('');
        //    $('#OverTime').val('');
        //    $('#OverTimeUnit').val('');
        //    $('#Note').val('');

        //    // 移除錯誤樣式
        //    $('.error-field').removeClass('error-field');
        //}

        //// 生成唯一ID
        //function generateId() {
        //    return 'charge_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        //}

        //// 顯示訊息
        //function showMessage(message, type) {
        //    var alertClass = 'alert-info';
        //    switch(type) {
        //        case 'success': alertClass = 'alert-success'; break;
        //        case 'error': alertClass = 'alert-danger'; break;
        //        case 'warning': alertClass = 'alert-warning'; break;
        //        default: alertClass = 'alert-info'; break;
        //    }

        //    var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible" role="alert" style="margin: 10px 0;">' +
        //                   '<button type="button" class="close" data-dismiss="alert"><span>&times;</span></button>' +
        //                   message +
        //                   '</div>';

        //    // 移除之前的訊息
        //    $('.alert').remove();

        //    // 在表格上方顯示訊息
        //    $('#tblChargeStandards').before(alertHtml);

        //    // 3秒後自動隱藏
        //    setTimeout(function() {
        //        $('.alert').fadeOut();
        //    }, 3000);
        //}

        //// 初始化顯示無資料訊息
        //$(document).ready(function() {
        //    renderChargeStandardsTable();
        //});




</script>

