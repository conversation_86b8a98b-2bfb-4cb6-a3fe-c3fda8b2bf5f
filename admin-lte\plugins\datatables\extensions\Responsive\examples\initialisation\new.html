<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">

	<title>Responsive example - `new` constructor</title>
	<link rel="stylesheet" type="text/css" href="../../../../media/css/jquery.dataTables.css">
	<link rel="stylesheet" type="text/css" href="../../css/dataTables.responsive.css">
	<link rel="stylesheet" type="text/css" href="../../../../examples/resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../../../../examples/resources/demo.css">
	<style type="text/css" class="init">

	div.container { max-width: 1200px }

	</style>
	<script type="text/javascript" language="javascript" src="../../../../media/js/jquery.js"></script>
	<script type="text/javascript" language="javascript" src="../../../../media/js/jquery.dataTables.js"></script>
	<script type="text/javascript" language="javascript" src="../../js/dataTables.responsive.js"></script>
	<script type="text/javascript" language="javascript" src="../../../../examples/resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../../../../examples/resources/demo.js"></script>
	<script type="text/javascript" language="javascript" class="init">



$(document).ready(function() {
	var table = $('#example').DataTable();

	new $.fn.dataTable.Responsive( table );
} );



	</script>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>Responsive example <span>`new` constructor</span></h1>

			<div class="info">
				<p>Responsive will automatically detect new DataTable instances being created on a page and initialise itself if it find the <a href=
				"//datatables.net/extensions/responsive/reference/option/responsive"><code class="option" title=
				"Responsive initialisation option">responsive<span>R</span></code></a> option or <code>responsive</code> class name on the table, as shown in the other
				examples.</p>

				<p>The third way of initialising Responsive is manually creating a new instance using the <code>$.fn.dataTable.Responsive</code> class, as shown in this example
				(the other two methods are provided using this constructor in a <a href="//datatables.net/reference/event/init"><code class="event" title=
				"DataTables event">init<span>DT</span></code></a> event handler!).</p>
			</div>

			<table id="example" class="display nowrap" cellspacing="0" width="100%">
				<thead>
					<tr>
						<th>First name</th>
						<th>Last name</th>
						<th>Position</th>
						<th>Office</th>
						<th>Age</th>
						<th>Start date</th>
						<th>Salary</th>
						<th>Extn.</th>
						<th>E-mail</th>
					</tr>
				</thead>

				<tbody>
					<tr>
						<td>Tiger</td>
						<td>Nixon</td>
						<td>System Architect</td>
						<td>Edinburgh</td>
						<td>61</td>
						<td>2011/04/25</td>
						<td>$320,800</td>
						<td>5421</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Garrett</td>
						<td>Winters</td>
						<td>Accountant</td>
						<td>Tokyo</td>
						<td>63</td>
						<td>2011/07/25</td>
						<td>$170,750</td>
						<td>8422</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Ashton</td>
						<td>Cox</td>
						<td>Junior Technical Author</td>
						<td>San Francisco</td>
						<td>66</td>
						<td>2009/01/12</td>
						<td>$86,000</td>
						<td>1562</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Cedric</td>
						<td>Kelly</td>
						<td>Senior Javascript Developer</td>
						<td>Edinburgh</td>
						<td>22</td>
						<td>2012/03/29</td>
						<td>$433,060</td>
						<td>6224</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Airi</td>
						<td>Satou</td>
						<td>Accountant</td>
						<td>Tokyo</td>
						<td>33</td>
						<td>2008/11/28</td>
						<td>$162,700</td>
						<td>5407</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Brielle</td>
						<td>Williamson</td>
						<td>Integration Specialist</td>
						<td>New York</td>
						<td>61</td>
						<td>2012/12/02</td>
						<td>$372,000</td>
						<td>4804</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Herrod</td>
						<td>Chandler</td>
						<td>Sales Assistant</td>
						<td>San Francisco</td>
						<td>59</td>
						<td>2012/08/06</td>
						<td>$137,500</td>
						<td>9608</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Rhona</td>
						<td>Davidson</td>
						<td>Integration Specialist</td>
						<td>Tokyo</td>
						<td>55</td>
						<td>2010/10/14</td>
						<td>$327,900</td>
						<td>6200</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Colleen</td>
						<td>Hurst</td>
						<td>Javascript Developer</td>
						<td>San Francisco</td>
						<td>39</td>
						<td>2009/09/15</td>
						<td>$205,500</td>
						<td>2360</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Sonya</td>
						<td>Frost</td>
						<td>Software Engineer</td>
						<td>Edinburgh</td>
						<td>23</td>
						<td>2008/12/13</td>
						<td>$103,600</td>
						<td>1667</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Jena</td>
						<td>Gaines</td>
						<td>Office Manager</td>
						<td>London</td>
						<td>30</td>
						<td>2008/12/19</td>
						<td>$90,560</td>
						<td>3814</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Quinn</td>
						<td>Flynn</td>
						<td>Support Lead</td>
						<td>Edinburgh</td>
						<td>22</td>
						<td>2013/03/03</td>
						<td>$342,000</td>
						<td>9497</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Charde</td>
						<td>Marshall</td>
						<td>Regional Director</td>
						<td>San Francisco</td>
						<td>36</td>
						<td>2008/10/16</td>
						<td>$470,600</td>
						<td>6741</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Haley</td>
						<td>Kennedy</td>
						<td>Senior Marketing Designer</td>
						<td>London</td>
						<td>43</td>
						<td>2012/12/18</td>
						<td>$313,500</td>
						<td>3597</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Tatyana</td>
						<td>Fitzpatrick</td>
						<td>Regional Director</td>
						<td>London</td>
						<td>19</td>
						<td>2010/03/17</td>
						<td>$385,750</td>
						<td>1965</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Michael</td>
						<td>Silva</td>
						<td>Marketing Designer</td>
						<td>London</td>
						<td>66</td>
						<td>2012/11/27</td>
						<td>$198,500</td>
						<td>1581</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Paul</td>
						<td>Byrd</td>
						<td>Chief Financial Officer (CFO)</td>
						<td>New York</td>
						<td>64</td>
						<td>2010/06/09</td>
						<td>$725,000</td>
						<td>3059</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Gloria</td>
						<td>Little</td>
						<td>Systems Administrator</td>
						<td>New York</td>
						<td>59</td>
						<td>2009/04/10</td>
						<td>$237,500</td>
						<td>1721</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Bradley</td>
						<td>Greer</td>
						<td>Software Engineer</td>
						<td>London</td>
						<td>41</td>
						<td>2012/10/13</td>
						<td>$132,000</td>
						<td>2558</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Dai</td>
						<td>Rios</td>
						<td>Personnel Lead</td>
						<td>Edinburgh</td>
						<td>35</td>
						<td>2012/09/26</td>
						<td>$217,500</td>
						<td>2290</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Jenette</td>
						<td>Caldwell</td>
						<td>Development Lead</td>
						<td>New York</td>
						<td>30</td>
						<td>2011/09/03</td>
						<td>$345,000</td>
						<td>1937</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Yuri</td>
						<td>Berry</td>
						<td>Chief Marketing Officer (CMO)</td>
						<td>New York</td>
						<td>40</td>
						<td>2009/06/25</td>
						<td>$675,000</td>
						<td>6154</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Caesar</td>
						<td>Vance</td>
						<td>Pre-Sales Support</td>
						<td>New York</td>
						<td>21</td>
						<td>2011/12/12</td>
						<td>$106,450</td>
						<td>8330</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Doris</td>
						<td>Wilder</td>
						<td>Sales Assistant</td>
						<td>Sidney</td>
						<td>23</td>
						<td>2010/09/20</td>
						<td>$85,600</td>
						<td>3023</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Angelica</td>
						<td>Ramos</td>
						<td>Chief Executive Officer (CEO)</td>
						<td>London</td>
						<td>47</td>
						<td>2009/10/09</td>
						<td>$1,200,000</td>
						<td>5797</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Gavin</td>
						<td>Joyce</td>
						<td>Developer</td>
						<td>Edinburgh</td>
						<td>42</td>
						<td>2010/12/22</td>
						<td>$92,575</td>
						<td>8822</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Jennifer</td>
						<td>Chang</td>
						<td>Regional Director</td>
						<td>Singapore</td>
						<td>28</td>
						<td>2010/11/14</td>
						<td>$357,650</td>
						<td>9239</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Brenden</td>
						<td>Wagner</td>
						<td>Software Engineer</td>
						<td>San Francisco</td>
						<td>28</td>
						<td>2011/06/07</td>
						<td>$206,850</td>
						<td>1314</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Fiona</td>
						<td>Green</td>
						<td>Chief Operating Officer (COO)</td>
						<td>San Francisco</td>
						<td>48</td>
						<td>2010/03/11</td>
						<td>$850,000</td>
						<td>2947</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Shou</td>
						<td>Itou</td>
						<td>Regional Marketing</td>
						<td>Tokyo</td>
						<td>20</td>
						<td>2011/08/14</td>
						<td>$163,000</td>
						<td>8899</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Michelle</td>
						<td>House</td>
						<td>Integration Specialist</td>
						<td>Sidney</td>
						<td>37</td>
						<td>2011/06/02</td>
						<td>$95,400</td>
						<td>2769</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Suki</td>
						<td>Burks</td>
						<td>Developer</td>
						<td>London</td>
						<td>53</td>
						<td>2009/10/22</td>
						<td>$114,500</td>
						<td>6832</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Prescott</td>
						<td>Bartlett</td>
						<td>Technical Author</td>
						<td>London</td>
						<td>27</td>
						<td>2011/05/07</td>
						<td>$145,000</td>
						<td>3606</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Gavin</td>
						<td>Cortez</td>
						<td>Team Leader</td>
						<td>San Francisco</td>
						<td>22</td>
						<td>2008/10/26</td>
						<td>$235,500</td>
						<td>2860</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Martena</td>
						<td>Mccray</td>
						<td>Post-Sales support</td>
						<td>Edinburgh</td>
						<td>46</td>
						<td>2011/03/09</td>
						<td>$324,050</td>
						<td>8240</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Unity</td>
						<td>Butler</td>
						<td>Marketing Designer</td>
						<td>San Francisco</td>
						<td>47</td>
						<td>2009/12/09</td>
						<td>$85,675</td>
						<td>5384</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Howard</td>
						<td>Hatfield</td>
						<td>Office Manager</td>
						<td>San Francisco</td>
						<td>51</td>
						<td>2008/12/16</td>
						<td>$164,500</td>
						<td>7031</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Hope</td>
						<td>Fuentes</td>
						<td>Secretary</td>
						<td>San Francisco</td>
						<td>41</td>
						<td>2010/02/12</td>
						<td>$109,850</td>
						<td>6318</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Vivian</td>
						<td>Harrell</td>
						<td>Financial Controller</td>
						<td>San Francisco</td>
						<td>62</td>
						<td>2009/02/14</td>
						<td>$452,500</td>
						<td>9422</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Timothy</td>
						<td>Mooney</td>
						<td>Office Manager</td>
						<td>London</td>
						<td>37</td>
						<td>2008/12/11</td>
						<td>$136,200</td>
						<td>7580</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Jackson</td>
						<td>Bradshaw</td>
						<td>Director</td>
						<td>New York</td>
						<td>65</td>
						<td>2008/09/26</td>
						<td>$645,750</td>
						<td>1042</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Olivia</td>
						<td>Liang</td>
						<td>Support Engineer</td>
						<td>Singapore</td>
						<td>64</td>
						<td>2011/02/03</td>
						<td>$234,500</td>
						<td>2120</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Bruno</td>
						<td>Nash</td>
						<td>Software Engineer</td>
						<td>London</td>
						<td>38</td>
						<td>2011/05/03</td>
						<td>$163,500</td>
						<td>6222</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Sakura</td>
						<td>Yamamoto</td>
						<td>Support Engineer</td>
						<td>Tokyo</td>
						<td>37</td>
						<td>2009/08/19</td>
						<td>$139,575</td>
						<td>9383</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Thor</td>
						<td>Walton</td>
						<td>Developer</td>
						<td>New York</td>
						<td>61</td>
						<td>2013/08/11</td>
						<td>$98,540</td>
						<td>8327</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Finn</td>
						<td>Camacho</td>
						<td>Support Engineer</td>
						<td>San Francisco</td>
						<td>47</td>
						<td>2009/07/07</td>
						<td>$87,500</td>
						<td>2927</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Serge</td>
						<td>Baldwin</td>
						<td>Data Coordinator</td>
						<td>Singapore</td>
						<td>64</td>
						<td>2012/04/09</td>
						<td>$138,575</td>
						<td>8352</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Zenaida</td>
						<td>Frank</td>
						<td>Software Engineer</td>
						<td>New York</td>
						<td>63</td>
						<td>2010/01/04</td>
						<td>$125,250</td>
						<td>7439</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Zorita</td>
						<td>Serrano</td>
						<td>Software Engineer</td>
						<td>San Francisco</td>
						<td>56</td>
						<td>2012/06/01</td>
						<td>$115,000</td>
						<td>4389</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Jennifer</td>
						<td>Acosta</td>
						<td>Junior Javascript Developer</td>
						<td>Edinburgh</td>
						<td>43</td>
						<td>2013/02/01</td>
						<td>$75,650</td>
						<td>3431</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Cara</td>
						<td>Stevens</td>
						<td>Sales Assistant</td>
						<td>New York</td>
						<td>46</td>
						<td>2011/12/06</td>
						<td>$145,600</td>
						<td>3990</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Hermione</td>
						<td>Butler</td>
						<td>Regional Director</td>
						<td>London</td>
						<td>47</td>
						<td>2011/03/21</td>
						<td>$356,250</td>
						<td>1016</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Lael</td>
						<td>Greer</td>
						<td>Systems Administrator</td>
						<td>London</td>
						<td>21</td>
						<td>2009/02/27</td>
						<td>$103,500</td>
						<td>6733</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Jonas</td>
						<td>Alexander</td>
						<td>Developer</td>
						<td>San Francisco</td>
						<td>30</td>
						<td>2010/07/14</td>
						<td>$86,500</td>
						<td>8196</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Shad</td>
						<td>Decker</td>
						<td>Regional Director</td>
						<td>Edinburgh</td>
						<td>51</td>
						<td>2008/11/13</td>
						<td>$183,000</td>
						<td>6373</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Michael</td>
						<td>Bruce</td>
						<td>Javascript Developer</td>
						<td>Singapore</td>
						<td>29</td>
						<td>2011/06/27</td>
						<td>$183,000</td>
						<td>5384</td>
						<td><EMAIL></td>
					</tr>
					<tr>
						<td>Donna</td>
						<td>Snider</td>
						<td>Customer Support</td>
						<td>New York</td>
						<td>27</td>
						<td>2011/01/25</td>
						<td>$112,000</td>
						<td>4226</td>
						<td><EMAIL></td>
					</tr>
				</tbody>
			</table>

			<ul class="tabs">
				<li class="active">Javascript</li>
				<li>HTML</li>
				<li>CSS</li>
				<li>Ajax</li>
				<li>Server-side script</li>
			</ul>

			<div class="tabs">
				<div class="js">
					<p>The Javascript shown below is used to initialise the table shown in this example:</p><code class="multiline language-js">$(document).ready(function() {
	var table = $('#example').DataTable();

	new $.fn.dataTable.Responsive( table );
} );</code>

					<p>In addition to the above code, the following Javascript library files are loaded for use in this example:</p>

					<ul>
						<li><a href="../../../../media/js/jquery.js">../../../../media/js/jquery.js</a></li>
						<li><a href="../../../../media/js/jquery.dataTables.js">../../../../media/js/jquery.dataTables.js</a></li>
						<li><a href="../../js/dataTables.responsive.js">../../js/dataTables.responsive.js</a></li>
					</ul>
				</div>

				<div class="table">
					<p>The HTML shown below is the raw HTML table element, before it has been enhanced by DataTables:</p>
				</div>

				<div class="css">
					<div>
						<p>This example uses a little bit of additional CSS beyond what is loaded from the library files (below), in order to correctly display the table. The
						additional CSS used is shown below:</p><code class="multiline language-css">div.container { max-width: 1200px }</code>
					</div>

					<p>The following CSS library files are loaded for use in this example to provide the styling of the table:</p>

					<ul>
						<li><a href="../../../../media/css/jquery.dataTables.css">../../../../media/css/jquery.dataTables.css</a></li>
						<li><a href="../../css/dataTables.responsive.css">../../css/dataTables.responsive.css</a></li>
					</ul>
				</div>

				<div class="ajax">
					<p>This table loads data by Ajax. The latest data that has been loaded is shown below. This data will update automatically as any additional data is
					loaded.</p>
				</div>

				<div class="php">
					<p>The script used to perform the server-side processing for this table is shown below. Please note that this is just an example script using PHP. Server-side
					processing scripts can be written in any language, using <a href="//datatables.net/manual/server-side">the protocol described in the DataTables
					documentation</a>.</p>
				</div>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<h2>Other examples</h2>

				<div class="toc">
					<div class="toc-group">
						<h3><a href="./index.html">Basic initialisation</a></h3>
						<ul class="toc active">
							<li><a href="./className.html">Class name</a></li>
							<li><a href="./option.html">Configuration option</a></li>
							<li class="active"><a href="./new.html">`new` constructor</a></li>
							<li><a href="./ajax.html">Ajax data</a></li>
							<li><a href="./default.html">Default initialisation</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="../styling/index.html">Styling</a></h3>
						<ul class="toc">
							<li><a href="../styling/bootstrap.html">Bootstrap styling</a></li>
							<li><a href="../styling/foundation.html">Foundation styling</a></li>
							<li><a href="../styling/scrolling.html">Vertical scrolling</a></li>
							<li><a href="../styling/compact.html">Compact styling</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="../display-control/index.html">Display control</a></h3>
						<ul class="toc">
							<li><a href="../display-control/auto.html">Automatic column hiding</a></li>
							<li><a href="../display-control/classes.html">Class control</a></li>
							<li><a href="../display-control/init-classes.html">Assigned class control</a></li>
							<li><a href="../display-control/fixedHeader.html">With FixedHeader</a></li>
							<li><a href="../display-control/complexHeader.html">Complex headers (rowspan / colspan)</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="../child-rows/index.html">Child rows</a></h3>
						<ul class="toc">
							<li><a href="../child-rows/disable-child-rows.html">Disable child rows</a></li>
							<li><a href="../child-rows/column-control.html">Column controlled child rows</a></li>
							<li><a href="../child-rows/right-column.html">Column control - right</a></li>
							<li><a href="../child-rows/whole-row-control.html">Whole row child row control</a></li>
							<li><a href="../child-rows/custom-renderer.html">Custom child row renderer</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extras">extras</a> and <a href="http://www.datatables.net/plug-ins">plug-ins</a>
					which extend the capabilities of DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href="http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2015<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>